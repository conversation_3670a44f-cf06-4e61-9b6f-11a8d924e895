import React, { useState } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Box,
  useTheme,
  useMediaQuery,
  Menu,
  MenuItem,
  Collapse,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Close as CloseIcon,
  ExpandMore,
  ExpandLess,
} from '@mui/icons-material'

const Header = () => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [anchorEl, setAnchorEl] = useState(null)
  const [openSubmenu, setOpenSubmenu] = useState('')
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'))

  const menuItems = [
    {
      label: 'Our School',
      submenu: [
        { label: 'About NTSS', href: '#about' },
        { label: "Principal's Message", href: '#principal' },
        { label: 'University Acceptances', href: '#university' },
        { label: 'Facilities', href: '#facilities' },
        { label: 'Policies & Procedures', href: '#policies' },
        { label: 'Work With Us', href: '#careers' },
      ]
    },
    {
      label: 'Admissions',
      submenu: [
        { label: 'Admissions', href: '#admissions' },
        { label: 'School Fees Structure', href: '#fees' },
      ]
    },
    {
      label: 'Academic Life',
      submenu: [
        { label: 'Academic Staff', href: '#staff' },
        { label: 'Engineering Programs', href: '#engineering' },
        { label: 'Computer Science', href: '#computer' },
        { label: 'Technical Skills', href: '#technical' },
        { label: 'ICDL', href: '#icdl' },
      ]
    },
    {
      label: 'Student Life',
      submenu: [
        { label: 'Discover NTSS', href: '#discover' },
        { label: 'School Calendar', href: '#calendar' },
        { label: 'Clubs & Societies', href: '#clubs' },
        { label: 'Sports at NTSS', href: '#sports' },
        { label: 'Students Handbook', href: '#handbook' },
      ]
    },
    { label: 'Alumni', href: '#alumni' },
    { label: 'Contacts', href: '#contacts' },
  ]

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleMenuClick = (event, menuLabel) => {
    if (isMobile) {
      setOpenSubmenu(openSubmenu === menuLabel ? '' : menuLabel)
    } else {
      setAnchorEl(event.currentTarget)
      setOpenSubmenu(menuLabel)
    }
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setOpenSubmenu('')
  }

  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setMobileOpen(false)
    handleMenuClose()
  }

  const drawer = (
    <Box sx={{ width: 280, pt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', px: 2, mb: 2 }}>
        <img
          src="https://rivierahighschool.org/wp-content/uploads/2023/03/logo.png"
          alt="NTSS Logo"
          style={{ height: '40px' }}
        />
        <IconButton onClick={handleDrawerToggle}>
          <CloseIcon />
        </IconButton>
      </Box>
      <List>
        {menuItems.map((item) => (
          <Box key={item.label}>
            <ListItem
              button
              onClick={() => item.submenu ? handleMenuClick(null, item.label) : scrollToSection(item.href)}
              sx={{
                '&:hover': {
                  backgroundColor: '#1976d2',
                  color: 'white',
                }
              }}
            >
              <ListItemText primary={item.label} />
              {item.submenu && (
                openSubmenu === item.label ? <ExpandLess /> : <ExpandMore />
              )}
            </ListItem>
            {item.submenu && (
              <Collapse in={openSubmenu === item.label} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {item.submenu.map((subItem) => (
                    <ListItem
                      key={subItem.label}
                      button
                      sx={{ pl: 4 }}
                      onClick={() => scrollToSection(subItem.href)}
                    >
                      <ListItemText primary={subItem.label} />
                    </ListItem>
                  ))}
                </List>
              </Collapse>
            )}
          </Box>
        ))}
      </List>
    </Box>
  )

  return (
    <>
      <AppBar
        position="fixed"
        sx={{
          backgroundColor: 'white',
          color: '#333',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          zIndex: 1300,
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>
          {/* Logo */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <img
              src="https://rivierahighschool.org/wp-content/uploads/2023/03/logo.png"
              alt="NTSS Logo"
              style={{ height: '50px', cursor: 'pointer' }}
              onClick={() => scrollToSection('#home')}
            />
          </Box>

          {/* Desktop Navigation */}
          {!isMobile && (
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              {menuItems.map((item) => (
                <Box key={item.label}>
                  <Button
                    onClick={(e) => item.submenu ? handleMenuClick(e, item.label) : scrollToSection(item.href)}
                    endIcon={item.submenu ? <ExpandMore /> : null}
                    sx={{
                      color: '#333',
                      fontWeight: 500,
                      fontSize: '0.9rem',
                      textTransform: 'none',
                      px: 2,
                      py: 1,
                      '&:hover': {
                        backgroundColor: '#1976d2',
                        color: 'white',
                      },
                    }}
                  >
                    {item.label}
                  </Button>
                  {item.submenu && (
                    <Menu
                      anchorEl={anchorEl}
                      open={Boolean(anchorEl) && openSubmenu === item.label}
                      onClose={handleMenuClose}
                      sx={{
                        '& .MuiPaper-root': {
                          mt: 1,
                          minWidth: 200,
                          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                        }
                      }}
                    >
                      {item.submenu.map((subItem) => (
                        <MenuItem
                          key={subItem.label}
                          onClick={() => scrollToSection(subItem.href)}
                          sx={{
                            fontSize: '0.9rem',
                            '&:hover': {
                              backgroundColor: '#1976d2',
                              color: 'white',
                            }
                          }}
                        >
                          {subItem.label}
                        </MenuItem>
                      ))}
                    </Menu>
                  )}
                </Box>
              ))}
              <Button
                variant="outlined"
                sx={{
                  ml: 2,
                  color: '#1976d2',
                  borderColor: '#1976d2',
                  textTransform: 'none',
                  '&:hover': {
                    backgroundColor: '#1976d2',
                    color: 'white',
                  }
                }}
              >
                Login
              </Button>
            </Box>
          )}

          {/* Mobile Menu Button */}
          {isMobile && (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
            >
              <MenuIcon />
            </IconButton>
          )}
        </Toolbar>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
      >
        {drawer}
      </Drawer>

      {/* Spacer for fixed header */}
      <Toolbar sx={{ minHeight: '70px' }} />
    </>
  )
}

export default Header
