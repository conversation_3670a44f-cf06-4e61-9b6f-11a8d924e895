import { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Avatar,
  Grid,
  Fade,
  Zoom,
} from '@mui/material'
import {
  School,
  Science,
  Computer,
  Engineering,
  Psychology,
  Business,
} from '@mui/icons-material'

const StaffSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [visibleCards, setVisibleCards] = useState([])

  // Staff data with gamified elements
  const staffMembers = [
    {
      id: 1,
      name: 'Eng. NGABONZIZA Germain',
      position: 'School Manager',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face',
      icon: <School />,
      color: '#1976d2',
    },
    {
      id: 2,
      name: 'NYIRUMURINGA Peter',
      position: 'Secretary',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face',
      level: 'Expert',
      experience: '12+ Years',
      specialty: 'Mechanical Engineering',
      icon: <Engineering />,
      color: '#ff9800',
      achievements: ['Research Excellence', 'Industry Partnership'],
    },
    {
      id: 3,
      name: 'Dr. <PERSON> Nkurunziza',
      position: 'ICT & Computer Science Head',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
      level: 'Expert',
      experience: '10+ Years',
      specialty: 'Software Development',
      icon: <Computer />,
      color: '#4caf50',
      achievements: ['Tech Innovation Award', 'Digital Transformation'],
    },
    {
      id: 4,
      name: 'Dr. Agnes Nyirahabimana',
      position: 'Science Department Head',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face',
      level: 'Expert',
      experience: '11+ Years',
      specialty: 'Applied Sciences',
      icon: <Science />,
      color: '#e91e63',
      achievements: ['Research Publication', 'Lab Excellence'],
    },
    {
      id: 5,
      name: 'Mr. Emmanuel Habimana',
      position: 'Student Affairs Coordinator',
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=300&fit=crop&crop=face',
      level: 'Advanced',
      experience: '8+ Years',
      specialty: 'Student Development',
      icon: <Psychology />,
      color: '#9c27b0',
      achievements: ['Student Choice Award', 'Mentorship Excellence'],
    },
    {
      id: 6,
      name: 'Ms. Claudine Uwimana',
      position: 'Business Studies Head',
      image: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=300&h=300&fit=crop&crop=face',
      level: 'Advanced',
      experience: '9+ Years',
      specialty: 'Business Administration',
      icon: <Business />,
      color: '#ff5722',
      achievements: ['Entrepreneurship Program', 'Industry Connect'],
    },
  ]

  useEffect(() => {
    // Initial load - show first 3 cards
    const timer1 = setTimeout(() => {
      setVisibleCards([0, 1, 2])
    }, 500)

    // Slideshow effect - switch to next 3 cards every 10 seconds
    const slideInterval = setInterval(() => {
      setCurrentSlide(prev => {
        const nextSlide = prev === 0 ? 1 : 0
        const startIndex = nextSlide * 3
        setVisibleCards([startIndex, startIndex + 1, startIndex + 2])
        return nextSlide
      })
    }, 10000)

    return () => {
      clearTimeout(timer1)
      clearInterval(slideInterval)
    }
  }, [])

  return (
    <Box
      id="staff"
      sx={{
        py: 10,
        background: 'linear-gradient(135deg, #6c757d 0%, #495057 100%)',
        backgroundImage: `
          url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"),
          linear-gradient(135deg, #6c757d 0%, #495057 100%)
        `,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `
            radial-gradient(circle at 20% 80%, rgba(108, 117, 125, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(73, 80, 87, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(134, 142, 150, 0.2) 0%, transparent 50%)
          `,
          zIndex: 1,
        }
      }}
    >
      {/* Floating Orbs Background */}
      {[...Array(12)].map((_, i) => (
        <Box
          key={i}
          sx={{
            position: 'absolute',
            width: Math.random() * 150 + 100,
            height: Math.random() * 150 + 100,
            background: `radial-gradient(circle, ${
              i % 3 === 0
                ? 'rgba(255, 255, 255, 0.1)'
                : i % 3 === 1
                ? 'rgba(255, 107, 53, 0.15)'
                : 'rgba(33, 150, 243, 0.15)'
            } 0%, transparent 70%)`,
            borderRadius: '50%',
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            animation: `floatOrb ${Math.random() * 10 + 8}s ease-in-out infinite alternate`,
            '@keyframes floatOrb': {
              '0%': {
                transform: 'translateY(0px) translateX(0px) scale(1)',
                opacity: 0.3,
              },
              '100%': {
                transform: 'translateY(-50px) translateX(30px) scale(1.2)',
                opacity: 0.1,
              },
            },
          }}
        />
      ))}

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography
              variant="h2"
              sx={{
                color: 'white',
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2.2rem', sm: '2.8rem', md: '3.2rem' },
                textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
                position: 'relative',
                animation: 'titleGlow 3s ease-in-out infinite alternate',
                '@keyframes titleGlow': {
                  '0%': { textShadow: '2px 2px 4px rgba(0,0,0,0.3)' },
                  '100%': { textShadow: '0 0 20px rgba(255,255,255,0.3), 2px 2px 4px rgba(0,0,0,0.3)' },
                },
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -10,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 4,
                  background: 'linear-gradient(90deg, #ff6b35, #ffffff)',
                  borderRadius: 2,
                  boxShadow: '0 2px 10px rgba(255, 107, 53, 0.5)',
                },
              }}
            >
              Meet Our Expert Staff
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'rgba(255, 255, 255, 0.9)',
                maxWidth: '600px',
                mx: 'auto',
                fontSize: { xs: '1.1rem', sm: '1.3rem' },
                fontWeight: 400,
                lineHeight: 1.6,
                mt: 3,
              }}
            >
              Dedicated professionals shaping the future of technical education
            </Typography>
          </Box>
        </Fade>

        <Grid container spacing={4} justifyContent="center">
          {staffMembers.slice(currentSlide * 3, (currentSlide + 1) * 3).map((staff, index) => (
            <Grid item xs={12} sm={6} md={4} key={staff.id}>
              <Zoom
                in={visibleCards.includes(currentSlide * 3 + index)}
                timeout={500 + index * 200}
                style={{ transitionDelay: `${index * 200}ms` }}
              >
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(20px)',
                    borderRadius: 4,
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                    '&:hover': {
                      transform: 'translateY(-15px) scale(1.02)',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2)',
                      '& .staff-avatar': {
                        transform: 'scale(1.1)',
                      },
                      '& .staff-name': {
                        color: staff.color,
                      },
                      '& .staff-position': {
                        color: '#2c3e50',
                      },
                    },
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    {/* Simple Avatar */}
                    <Box sx={{ mb: 3 }}>
                      <Avatar
                        className="staff-avatar"
                        src={staff.image}
                        sx={{
                          width: 120,
                          height: 120,
                          mx: 'auto',
                          mb: 2,
                          border: `4px solid ${staff.color}`,
                          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                          boxShadow: `0 8px 30px ${staff.color}40`,
                        }}
                      />
                    </Box>

                    {/* Staff Name */}
                    <Typography
                      className="staff-name"
                      variant="h5"
                      sx={{
                        fontWeight: 700,
                        mb: 1.5,
                        color: '#2c3e50',
                        fontSize: '1.3rem',
                        transition: 'color 0.3s ease',
                        lineHeight: 1.2,
                      }}
                    >
                      {staff.name}
                    </Typography>

                    {/* Staff Position */}
                    <Typography
                      className="staff-position"
                      variant="body1"
                      sx={{
                        color: staff.color,
                        fontWeight: 600,
                        fontSize: '1rem',
                        lineHeight: 1.4,
                        transition: 'color 0.3s ease',
                      }}
                    >
                      {staff.position}
                    </Typography>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>

        {/* Elegant Slide Indicators */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 6, gap: 2 }}>
          {[0, 1].map((slide) => (
            <Box
              key={slide}
              sx={{
                width: currentSlide === slide ? 40 : 12,
                height: 6,
                borderRadius: 3,
                bgcolor: currentSlide === slide ? '#ffffff' : 'rgba(255, 255, 255, 0.3)',
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                cursor: 'pointer',
                position: 'relative',
                boxShadow: currentSlide === slide ? '0 2px 10px rgba(255, 255, 255, 0.3)' : 'none',
                '&:hover': {
                  bgcolor: currentSlide === slide ? '#ffffff' : 'rgba(255, 255, 255, 0.6)',
                  transform: 'scale(1.1)',
                  boxShadow: '0 2px 10px rgba(255, 255, 255, 0.4)',
                },
              }}
              onClick={() => {
                setCurrentSlide(slide)
                const startIndex = slide * 3
                setVisibleCards([startIndex, startIndex + 1, startIndex + 2])
              }}
            />
          ))}
        </Box>
      </Container>
    </Box>
  )
}

export default StaffSection
