import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  Grid,
  Fade,
  Slide,
  Zoom,
  IconButton,
} from '@mui/material'
import {
  School,
  Science,
  Computer,
  Engineering,
  Psychology,
  Business,
  Star,
  EmojiEvents,
  TrendingUp,
} from '@mui/icons-material'

const StaffSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [visibleCards, setVisibleCards] = useState([])

  // Staff data with gamified elements
  const staffMembers = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      position: 'Principal & Academic Director',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face',
      level: 'Master',
      experience: '15+ Years',
      specialty: 'Educational Leadership',
      icon: <School />,
      color: '#1976d2',
      achievements: ['Excellence Award 2023', 'Innovation Leader'],
    },
    {
      id: 2,
      name: 'Prof. <PERSON>',
      position: 'Head of Engineering Department',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face',
      level: 'Expert',
      experience: '12+ Years',
      specialty: 'Mechanical Engineering',
      icon: <Engineering />,
      color: '#ff9800',
      achievements: ['Research Excellence', 'Industry Partnership'],
    },
    {
      id: 3,
      name: 'Dr. Patrick Nkurunziza',
      position: 'ICT & Computer Science Head',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
      level: 'Expert',
      experience: '10+ Years',
      specialty: 'Software Development',
      icon: <Computer />,
      color: '#4caf50',
      achievements: ['Tech Innovation Award', 'Digital Transformation'],
    },
    {
      id: 4,
      name: 'Dr. Agnes Nyirahabimana',
      position: 'Science Department Head',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face',
      level: 'Expert',
      experience: '11+ Years',
      specialty: 'Applied Sciences',
      icon: <Science />,
      color: '#e91e63',
      achievements: ['Research Publication', 'Lab Excellence'],
    },
    {
      id: 5,
      name: 'Mr. Emmanuel Habimana',
      position: 'Student Affairs Coordinator',
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=300&fit=crop&crop=face',
      level: 'Advanced',
      experience: '8+ Years',
      specialty: 'Student Development',
      icon: <Psychology />,
      color: '#9c27b0',
      achievements: ['Student Choice Award', 'Mentorship Excellence'],
    },
    {
      id: 6,
      name: 'Ms. Claudine Uwimana',
      position: 'Business Studies Head',
      image: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=300&h=300&fit=crop&crop=face',
      level: 'Advanced',
      experience: '9+ Years',
      specialty: 'Business Administration',
      icon: <Business />,
      color: '#ff5722',
      achievements: ['Entrepreneurship Program', 'Industry Connect'],
    },
  ]

  useEffect(() => {
    // Initial load - show first 3 cards
    const timer1 = setTimeout(() => {
      setVisibleCards([0, 1, 2])
    }, 500)

    // Slideshow effect - switch to next 3 cards every 4 seconds
    const slideInterval = setInterval(() => {
      setCurrentSlide(prev => {
        const nextSlide = prev === 0 ? 1 : 0
        const startIndex = nextSlide * 3
        setVisibleCards([startIndex, startIndex + 1, startIndex + 2])
        return nextSlide
      })
    }, 4000)

    return () => {
      clearTimeout(timer1)
      clearInterval(slideInterval)
    }
  }, [])

  const getLevelColor = (level) => {
    switch (level) {
      case 'Master': return '#ffd700'
      case 'Expert': return '#ff6b35'
      case 'Advanced': return '#4caf50'
      default: return '#2196f3'
    }
  }

  return (
    <Box
      id="staff"
      sx={{
        py: 8,
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.1)',
          zIndex: 1,
        }
      }}
    >
      {/* Floating Background Elements */}
      {[...Array(15)].map((_, i) => (
        <Box
          key={i}
          sx={{
            position: 'absolute',
            width: Math.random() * 100 + 50,
            height: Math.random() * 100 + 50,
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
            borderRadius: '50%',
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            animation: `float ${Math.random() * 6 + 4}s ease-in-out infinite alternate`,
            '@keyframes float': {
              '0%': { transform: 'translateY(0px) rotate(0deg)' },
              '100%': { transform: 'translateY(-30px) rotate(180deg)' },
            },
          }}
        />
      ))}

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography
              variant="h2"
              sx={{
                color: 'white',
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
                animation: 'glow 2s ease-in-out infinite alternate',
                '@keyframes glow': {
                  '0%': { textShadow: '2px 2px 4px rgba(0,0,0,0.3)' },
                  '100%': { textShadow: '0 0 20px rgba(255,255,255,0.5), 2px 2px 4px rgba(0,0,0,0.3)' },
                },
              }}
            >
              🌟 Meet Our Expert Staff
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'rgba(255,255,255,0.9)',
                maxWidth: '600px',
                mx: 'auto',
                fontSize: { xs: '1rem', sm: '1.2rem' },
              }}
            >
              Our dedicated team of professionals bringing excellence to technical education
            </Typography>
          </Box>
        </Fade>

        <Grid container spacing={4} justifyContent="center">
          {staffMembers.slice(currentSlide * 3, (currentSlide + 1) * 3).map((staff, index) => (
            <Grid item xs={12} sm={6} md={4} key={staff.id}>
              <Zoom
                in={visibleCards.includes(currentSlide * 3 + index)}
                timeout={500 + index * 200}
                style={{ transitionDelay: `${index * 200}ms` }}
              >
                <Card
                  sx={{
                    height: '100%',
                    background: 'rgba(255,255,255,0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 4,
                    border: '2px solid rgba(255,255,255,0.2)',
                    position: 'relative',
                    overflow: 'visible',
                    transition: 'all 0.4s ease',
                    '&:hover': {
                      transform: 'translateY(-15px) scale(1.02)',
                      boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
                      '& .staff-avatar': {
                        transform: 'scale(1.1) rotate(5deg)',
                      },
                      '& .level-chip': {
                        animation: 'bounce 0.6s ease-in-out',
                      },
                    },
                    '@keyframes bounce': {
                      '0%, 20%, 50%, 80%, 100%': { transform: 'translateY(0)' },
                      '40%': { transform: 'translateY(-10px)' },
                      '60%': { transform: 'translateY(-5px)' },
                    },
                  }}
                >
                  {/* Level Badge */}
                  <Chip
                    className="level-chip"
                    icon={<Star />}
                    label={staff.level}
                    sx={{
                      position: 'absolute',
                      top: -10,
                      right: 15,
                      bgcolor: getLevelColor(staff.level),
                      color: 'white',
                      fontWeight: 'bold',
                      zIndex: 3,
                      boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
                    }}
                  />

                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    {/* Avatar with Icon */}
                    <Box sx={{ position: 'relative', mb: 2 }}>
                      <Avatar
                        className="staff-avatar"
                        src={staff.image}
                        sx={{
                          width: 100,
                          height: 100,
                          mx: 'auto',
                          mb: 1,
                          border: `4px solid ${staff.color}`,
                          transition: 'all 0.3s ease',
                          boxShadow: `0 8px 25px ${staff.color}40`,
                        }}
                      />
                      <Avatar
                        sx={{
                          position: 'absolute',
                          bottom: -5,
                          right: 'calc(50% - 65px)',
                          width: 35,
                          height: 35,
                          bgcolor: staff.color,
                          border: '3px solid white',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
                        }}
                      >
                        {staff.icon}
                      </Avatar>
                    </Box>

                    {/* Staff Info */}
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        mb: 1,
                        color: '#2c3e50',
                        fontSize: '1.1rem',
                      }}
                    >
                      {staff.name}
                    </Typography>

                    <Typography
                      variant="body1"
                      sx={{
                        color: staff.color,
                        fontWeight: 600,
                        mb: 2,
                        fontSize: '0.95rem',
                      }}
                    >
                      {staff.position}
                    </Typography>

                    {/* Experience & Specialty */}
                    <Box sx={{ mb: 2 }}>
                      <Chip
                        icon={<EmojiEvents />}
                        label={staff.experience}
                        size="small"
                        sx={{
                          bgcolor: 'rgba(76, 175, 80, 0.1)',
                          color: '#4caf50',
                          mr: 1,
                          mb: 1,
                        }}
                      />
                      <Chip
                        label={staff.specialty}
                        size="small"
                        sx={{
                          bgcolor: 'rgba(33, 150, 243, 0.1)',
                          color: '#2196f3',
                        }}
                      />
                    </Box>

                    {/* Achievements */}
                    <Box>
                      {staff.achievements.map((achievement, idx) => (
                        <Chip
                          key={idx}
                          icon={<TrendingUp />}
                          label={achievement}
                          size="small"
                          sx={{
                            bgcolor: 'rgba(255, 152, 0, 0.1)',
                            color: '#ff9800',
                            mr: 0.5,
                            mb: 0.5,
                            fontSize: '0.75rem',
                          }}
                        />
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>

        {/* Slide Indicators */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4, gap: 1 }}>
          {[0, 1].map((slide) => (
            <Box
              key={slide}
              sx={{
                width: 12,
                height: 12,
                borderRadius: '50%',
                bgcolor: currentSlide === slide ? '#ff6b35' : 'rgba(255,255,255,0.3)',
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: '#ff6b35',
                  transform: 'scale(1.2)',
                },
              }}
              onClick={() => {
                setCurrentSlide(slide)
                const startIndex = slide * 3
                setVisibleCards([startIndex, startIndex + 1, startIndex + 2])
              }}
            />
          ))}
        </Box>
      </Container>
    </Box>
  )
}

export default StaffSection
