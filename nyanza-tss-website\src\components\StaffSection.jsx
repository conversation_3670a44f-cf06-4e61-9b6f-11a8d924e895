import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  Grid,
  Fade,
  Slide,
  Zoom,
  IconButton,
} from '@mui/material'
import {
  School,
  Science,
  Computer,
  Engineering,
  Psychology,
  Business,
  Star,
  EmojiEvents,
  TrendingUp,
} from '@mui/icons-material'

const StaffSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [visibleCards, setVisibleCards] = useState([])

  // Staff data with gamified elements
  const staffMembers = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      position: 'Principal & Academic Director',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face',
      level: 'Master',
      experience: '15+ Years',
      specialty: 'Educational Leadership',
      icon: <School />,
      color: '#1976d2',
      achievements: ['Excellence Award 2023', 'Innovation Leader'],
    },
    {
      id: 2,
      name: 'Prof. <PERSON>',
      position: 'Head of Engineering Department',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face',
      level: 'Expert',
      experience: '12+ Years',
      specialty: 'Mechanical Engineering',
      icon: <Engineering />,
      color: '#ff9800',
      achievements: ['Research Excellence', 'Industry Partnership'],
    },
    {
      id: 3,
      name: 'Dr. Patrick Nkurunziza',
      position: 'ICT & Computer Science Head',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
      level: 'Expert',
      experience: '10+ Years',
      specialty: 'Software Development',
      icon: <Computer />,
      color: '#4caf50',
      achievements: ['Tech Innovation Award', 'Digital Transformation'],
    },
    {
      id: 4,
      name: 'Dr. Agnes Nyirahabimana',
      position: 'Science Department Head',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face',
      level: 'Expert',
      experience: '11+ Years',
      specialty: 'Applied Sciences',
      icon: <Science />,
      color: '#e91e63',
      achievements: ['Research Publication', 'Lab Excellence'],
    },
    {
      id: 5,
      name: 'Mr. Emmanuel Habimana',
      position: 'Student Affairs Coordinator',
      image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=300&fit=crop&crop=face',
      level: 'Advanced',
      experience: '8+ Years',
      specialty: 'Student Development',
      icon: <Psychology />,
      color: '#9c27b0',
      achievements: ['Student Choice Award', 'Mentorship Excellence'],
    },
    {
      id: 6,
      name: 'Ms. Claudine Uwimana',
      position: 'Business Studies Head',
      image: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=300&h=300&fit=crop&crop=face',
      level: 'Advanced',
      experience: '9+ Years',
      specialty: 'Business Administration',
      icon: <Business />,
      color: '#ff5722',
      achievements: ['Entrepreneurship Program', 'Industry Connect'],
    },
  ]

  useEffect(() => {
    // Initial load - show first 3 cards
    const timer1 = setTimeout(() => {
      setVisibleCards([0, 1, 2])
    }, 500)

    // Slideshow effect - switch to next 3 cards every 10 seconds
    const slideInterval = setInterval(() => {
      setCurrentSlide(prev => {
        const nextSlide = prev === 0 ? 1 : 0
        const startIndex = nextSlide * 3
        setVisibleCards([startIndex, startIndex + 1, startIndex + 2])
        return nextSlide
      })
    }, 10000)

    return () => {
      clearTimeout(timer1)
      clearInterval(slideInterval)
    }
  }, [])

  const getLevelColor = (level) => {
    switch (level) {
      case 'Master': return '#ffd700'
      case 'Expert': return '#ff6b35'
      case 'Advanced': return '#4caf50'
      default: return '#2196f3'
    }
  }

  return (
    <Box
      id="staff"
      sx={{
        py: 10,
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(33, 150, 243, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(76, 175, 80, 0.05) 0%, transparent 50%)
          `,
          zIndex: 1,
        }
      }}
    >
      {/* Professional Geometric Background Elements */}
      {[...Array(8)].map((_, i) => (
        <Box
          key={i}
          sx={{
            position: 'absolute',
            width: i % 2 === 0 ? 120 : 80,
            height: i % 2 === 0 ? 120 : 80,
            background: i % 3 === 0
              ? 'linear-gradient(45deg, rgba(255, 107, 53, 0.08), rgba(255, 107, 53, 0.02))'
              : i % 3 === 1
              ? 'linear-gradient(45deg, rgba(33, 150, 243, 0.08), rgba(33, 150, 243, 0.02))'
              : 'linear-gradient(45deg, rgba(76, 175, 80, 0.08), rgba(76, 175, 80, 0.02))',
            borderRadius: i % 2 === 0 ? '50%' : '20%',
            top: `${Math.random() * 80 + 10}%`,
            left: `${Math.random() * 80 + 10}%`,
            animation: `professionalFloat ${Math.random() * 8 + 6}s ease-in-out infinite alternate`,
            '@keyframes professionalFloat': {
              '0%': { transform: 'translateY(0px) scale(1)' },
              '100%': { transform: 'translateY(-20px) scale(1.05)' },
            },
          }}
        />
      ))}

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        <Fade in timeout={1000}>
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography
              variant="h2"
              sx={{
                color: '#2c3e50',
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2.2rem', sm: '2.8rem', md: '3.2rem' },
                textShadow: '1px 1px 2px rgba(0,0,0,0.1)',
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -10,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 4,
                  background: 'linear-gradient(90deg, #ff6b35, #2196f3)',
                  borderRadius: 2,
                },
              }}
            >
              Meet Our Expert Faculty
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: '#546e7a',
                maxWidth: '700px',
                mx: 'auto',
                fontSize: { xs: '1.1rem', sm: '1.3rem' },
                fontWeight: 400,
                lineHeight: 1.6,
                mt: 3,
              }}
            >
              Distinguished educators and industry professionals committed to shaping the next generation of technical leaders through innovative teaching and mentorship
            </Typography>
          </Box>
        </Fade>

        <Grid container spacing={4} justifyContent="center">
          {staffMembers.slice(currentSlide * 3, (currentSlide + 1) * 3).map((staff, index) => (
            <Grid item xs={12} sm={6} md={4} key={staff.id}>
              <Zoom
                in={visibleCards.includes(currentSlide * 3 + index)}
                timeout={500 + index * 200}
                style={{ transitionDelay: `${index * 200}ms` }}
              >
                <Card
                  sx={{
                    height: '100%',
                    background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
                    borderRadius: 3,
                    border: '1px solid rgba(0,0,0,0.08)',
                    position: 'relative',
                    overflow: 'visible',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
                      '& .staff-avatar': {
                        transform: 'scale(1.05)',
                      },
                      '& .level-chip': {
                        transform: 'scale(1.05)',
                      },
                      '& .staff-name': {
                        color: staff.color,
                      },
                    },
                  }}
                >
                  {/* Level Badge */}
                  <Chip
                    className="level-chip"
                    icon={<Star sx={{ fontSize: 16 }} />}
                    label={staff.level}
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: 15,
                      right: 15,
                      bgcolor: getLevelColor(staff.level),
                      color: 'white',
                      fontWeight: 600,
                      fontSize: '0.75rem',
                      zIndex: 3,
                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                      transition: 'all 0.3s ease',
                      border: '2px solid white',
                    }}
                  />

                  <CardContent sx={{ textAlign: 'center', p: 4 }}>
                    {/* Avatar with Icon */}
                    <Box sx={{ position: 'relative', mb: 3 }}>
                      <Avatar
                        className="staff-avatar"
                        src={staff.image}
                        sx={{
                          width: 110,
                          height: 110,
                          mx: 'auto',
                          mb: 1,
                          border: `3px solid ${staff.color}`,
                          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                          boxShadow: `0 8px 25px ${staff.color}30`,
                        }}
                      />
                      <Avatar
                        sx={{
                          position: 'absolute',
                          bottom: 5,
                          right: 'calc(50% - 70px)',
                          width: 32,
                          height: 32,
                          bgcolor: staff.color,
                          border: '3px solid white',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                          transition: 'all 0.3s ease',
                        }}
                      >
                        {React.cloneElement(staff.icon, { sx: { fontSize: 18 } })}
                      </Avatar>
                    </Box>

                    {/* Staff Info */}
                    <Typography
                      className="staff-name"
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        mb: 1,
                        color: '#2c3e50',
                        fontSize: '1.15rem',
                        transition: 'color 0.3s ease',
                        lineHeight: 1.3,
                      }}
                    >
                      {staff.name}
                    </Typography>

                    <Typography
                      variant="body1"
                      sx={{
                        color: staff.color,
                        fontWeight: 600,
                        mb: 3,
                        fontSize: '0.9rem',
                        lineHeight: 1.4,
                        opacity: 0.9,
                      }}
                    >
                      {staff.position}
                    </Typography>

                    {/* Experience & Specialty */}
                    <Box sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>
                        <Chip
                          icon={<EmojiEvents sx={{ fontSize: 16 }} />}
                          label={staff.experience}
                          size="small"
                          sx={{
                            bgcolor: 'rgba(76, 175, 80, 0.12)',
                            color: '#388e3c',
                            fontWeight: 500,
                            fontSize: '0.8rem',
                            border: '1px solid rgba(76, 175, 80, 0.2)',
                          }}
                        />
                        <Chip
                          label={staff.specialty}
                          size="small"
                          sx={{
                            bgcolor: 'rgba(33, 150, 243, 0.12)',
                            color: '#1976d2',
                            fontWeight: 500,
                            fontSize: '0.8rem',
                            border: '1px solid rgba(33, 150, 243, 0.2)',
                          }}
                        />
                      </Box>
                    </Box>

                    {/* Achievements */}
                    <Box sx={{
                      borderTop: '1px solid rgba(0,0,0,0.08)',
                      pt: 2,
                      display: 'flex',
                      flexWrap: 'wrap',
                      justifyContent: 'center',
                      gap: 0.5,
                    }}>
                      {staff.achievements.map((achievement, idx) => (
                        <Chip
                          key={idx}
                          icon={<TrendingUp sx={{ fontSize: 14 }} />}
                          label={achievement}
                          size="small"
                          sx={{
                            bgcolor: 'rgba(255, 152, 0, 0.1)',
                            color: '#f57c00',
                            fontSize: '0.7rem',
                            fontWeight: 500,
                            border: '1px solid rgba(255, 152, 0, 0.2)',
                            '&:hover': {
                              bgcolor: 'rgba(255, 152, 0, 0.15)',
                            },
                          }}
                        />
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>

        {/* Professional Slide Indicators */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 6, gap: 2 }}>
          {[0, 1].map((slide) => (
            <Box
              key={slide}
              sx={{
                width: currentSlide === slide ? 40 : 12,
                height: 6,
                borderRadius: 3,
                bgcolor: currentSlide === slide ? '#ff6b35' : 'rgba(44, 62, 80, 0.2)',
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                cursor: 'pointer',
                position: 'relative',
                '&:hover': {
                  bgcolor: currentSlide === slide ? '#e55a2b' : 'rgba(44, 62, 80, 0.4)',
                  transform: 'scale(1.1)',
                },
                '&::after': currentSlide === slide ? {
                  content: '""',
                  position: 'absolute',
                  top: -2,
                  left: -2,
                  right: -2,
                  bottom: -2,
                  borderRadius: 4,
                  border: '1px solid rgba(255, 107, 53, 0.3)',
                } : {},
              }}
              onClick={() => {
                setCurrentSlide(slide)
                const startIndex = slide * 3
                setVisibleCards([startIndex, startIndex + 1, startIndex + 2])
              }}
            />
          ))}
        </Box>
      </Container>
    </Box>
  )
}

export default StaffSection
