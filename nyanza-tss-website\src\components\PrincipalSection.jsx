import React from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
} from '@mui/material'

const PrincipalSection = () => {
  return (
    <Box
      id="principal"
      sx={{
        py: 8,
        backgroundColor: 'white',
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={6} alignItems="center">
          {/* Principal Image */}
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <img
                src="https://rivierahighschool.org/wp-content/uploads/2024/05/principal.pngw_.png"
                alt="Principal"
                style={{
                  width: '100%',
                  maxWidth: '300px',
                  height: 'auto',
                  borderRadius: '8px',
                }}
              />
            </Box>
          </Grid>

          {/* Quote and Message */}
          <Grid item xs={12} md={8}>
            <Box sx={{ position: 'relative' }}>
              {/* Quote Icon */}
              <Box
                sx={{
                  position: 'absolute',
                  top: -20,
                  left: -20,
                  zIndex: 1,
                }}
              >
                <img
                  src="https://rivierahighschool.org/wp-content/uploads/2022/12/icon_Quote.png"
                  alt="Quote"
                  style={{
                    width: '60px',
                    height: '60px',
                    opacity: 0.3,
                  }}
                />
              </Box>

              <Typography
                variant="h5"
                sx={{
                  fontStyle: 'italic',
                  mb: 4,
                  color: '#333',
                  lineHeight: 1.6,
                  fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.5rem' },
                  position: 'relative',
                  zIndex: 2,
                }}
              >
                At Nyanza Technical Secondary School, we are committed to providing a safe, 
                nurturing, and inclusive environment where every student can thrive academically, 
                socially, and emotionally.
              </Typography>

              <Box>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 'bold',
                    color: '#333',
                    mb: 0.5,
                  }}
                >
                  John Doe,
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#666',
                    fontStyle: 'italic',
                  }}
                >
                  Principal, Nyanza Technical Secondary School
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default PrincipalSection
