import React from 'react'
import {
  Box,
  Container,
  Typography,
  Button,
  useTheme,
  useMediaQuery,
} from '@mui/material'

const HeroSection = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))

  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <Box
      id="home"
      sx={{
        minHeight: '100vh',
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundImage: 'url(https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 1,
        }
      }}
    >
      {/* Large Background Text */}
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 0,
          opacity: 0.1,
          pointerEvents: 'none',
          textAlign: 'center',
          width: '100%',
        }}
      >
        <Typography
          sx={{
            fontSize: { xs: '8rem', sm: '12rem', md: '16rem', lg: '20rem' },
            fontWeight: 900,
            color: 'white',
            lineHeight: 0.8,
            letterSpacing: '-0.05em',
          }}
        >
          THE
        </Typography>
        <Typography
          sx={{
            fontSize: { xs: '6rem', sm: '10rem', md: '14rem', lg: '18rem' },
            fontWeight: 900,
            color: 'white',
            lineHeight: 0.8,
            letterSpacing: '-0.05em',
            mt: -2,
          }}
        >
          OASIS
        </Typography>
      </Box>

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2, textAlign: 'center' }}>
        <Box sx={{ color: 'white' }}>
          <Typography
            variant="h6"
            sx={{
              fontSize: { xs: '1.2rem', sm: '1.5rem', md: '2rem' },
              fontWeight: 300,
              mb: 1,
              letterSpacing: '0.1em',
              textTransform: 'uppercase',
            }}
          >
            THE
          </Typography>

          <Typography
            variant="h1"
            sx={{
              fontSize: { xs: '3rem', sm: '4rem', md: '6rem', lg: '8rem' },
              fontWeight: 900,
              mb: 1,
              lineHeight: 0.9,
              letterSpacing: '-0.02em',
            }}
          >
            OASIS
          </Typography>

          <Typography
            variant="h6"
            sx={{
              fontSize: { xs: '1rem', sm: '1.2rem', md: '1.5rem' },
              fontWeight: 300,
              mb: 1,
              letterSpacing: '0.05em',
            }}
          >
            The
          </Typography>

          <Typography
            variant="h2"
            sx={{
              fontSize: { xs: '2rem', sm: '3rem', md: '4rem', lg: '5rem' },
              fontWeight: 900,
              mb: 4,
              lineHeight: 0.9,
              letterSpacing: '-0.02em',
            }}
          >
            OASIS
          </Typography>

          <Typography
            variant="h4"
            sx={{
              fontSize: { xs: '1.5rem', sm: '2rem', md: '3rem' },
              fontWeight: 300,
              mb: 6,
              letterSpacing: '0.05em',
            }}
          >
            of Knowledge
          </Typography>

          <Button
            variant="outlined"
            size="large"
            onClick={() => scrollToSection('#about')}
            sx={{
              borderColor: 'white',
              color: 'white',
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 500,
              textTransform: 'uppercase',
              letterSpacing: '0.1em',
              borderWidth: 2,
              '&:hover': {
                backgroundColor: 'white',
                color: 'black',
                borderColor: 'white',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s ease',
            }}
          >
            Discover More
          </Button>
        </Box>
      </Container>
    </Box>
  )
}

export default HeroSection
