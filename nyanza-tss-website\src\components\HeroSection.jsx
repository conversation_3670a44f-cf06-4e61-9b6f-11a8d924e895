import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Button,
  useTheme,
  useMediaQuery,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Fade,
  Slide,
  Zoom,
  Avatar,
  LinearProgress,
} from '@mui/material'
import {
  School,
  EmojiEvents,
  Groups,
  Science,
  Engineering,
  Computer,
  PlayArrow,
  Star,
  TrendingUp,
  Lightbulb,
  Rocket,
  AutoAwesome,
} from '@mui/icons-material'

const HeroSection = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const [animationStep, setAnimationStep] = useState(0)
  const [statsVisible, setStatsVisible] = useState(false)
  const [achievementProgress, setAchievementProgress] = useState(0)

  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  // Gamifying animations
  useEffect(() => {
    const timer1 = setTimeout(() => setAnimationStep(1), 500)
    const timer2 = setTimeout(() => setAnimationStep(2), 1000)
    const timer3 = setTimeout(() => setAnimationStep(3), 1500)
    const timer4 = setTimeout(() => setStatsVisible(true), 2000)

    // Achievement progress animation
    const progressTimer = setInterval(() => {
      setAchievementProgress(prev => {
        if (prev >= 100) return 100
        return prev + 2
      })
    }, 50)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
      clearTimeout(timer3)
      clearTimeout(timer4)
      clearInterval(progressTimer)
    }
  }, [])

  // Gamifying stats data
  const stats = [
    { icon: <School />, value: '15+', label: 'Years Excellence', color: '#1976d2' },
    { icon: <EmojiEvents />, value: '500+', label: 'Graduates', color: '#ff9800' },
    { icon: <Groups />, value: '1200+', label: 'Students', color: '#4caf50' },
    { icon: <Science />, value: '95%', label: 'Success Rate', color: '#e91e63' },
  ]

  const achievements = [
    { icon: <Engineering />, title: 'Engineering Excellence', level: 'Master', progress: 95 },
    { icon: <Computer />, title: 'Tech Innovation', level: 'Expert', progress: 88 },
    { icon: <Science />, title: 'Scientific Research', level: 'Advanced', progress: 92 },
  ]

  return (
    <Box
      id="home"
      sx={{
        minHeight: '100vh',
        position: 'relative',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: 'url(https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: 0.3,
          zIndex: 1,
        },
        '&::after': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%)',
          zIndex: 2,
        }
      }}
    >
      {/* Floating Particles */}
      {[...Array(20)].map((_, i) => (
        <Box
          key={i}
          sx={{
            position: 'absolute',
            width: Math.random() * 10 + 5,
            height: Math.random() * 10 + 5,
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '50%',
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            zIndex: 3,
            animation: `float ${Math.random() * 3 + 2}s ease-in-out infinite alternate`,
            '@keyframes float': {
              '0%': { transform: 'translateY(0px)' },
              '100%': { transform: 'translateY(-20px)' },
            },
          }}
        />
      ))}

      {/* Geometric Shapes */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          right: '10%',
          width: 100,
          height: 100,
          border: '2px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '50%',
          zIndex: 3,
          animation: 'rotate 20s linear infinite',
          '@keyframes rotate': {
            '0%': { transform: 'rotate(0deg)' },
            '100%': { transform: 'rotate(360deg)' },
          },
        }}
      />

      <Box
        sx={{
          position: 'absolute',
          bottom: '15%',
          left: '5%',
          width: 80,
          height: 80,
          backgroundColor: 'rgba(255, 152, 0, 0.2)',
          transform: 'rotate(45deg)',
          zIndex: 3,
          animation: 'pulse 3s ease-in-out infinite',
          '@keyframes pulse': {
            '0%, 100%': { transform: 'rotate(45deg) scale(1)' },
            '50%': { transform: 'rotate(45deg) scale(1.1)' },
          },
        }}
      />
      {/* Floating Gamifying Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 3,
          pointerEvents: 'none',
        }}
      >
        {/* Floating Icons */}
        {[...Array(8)].map((_, i) => (
          <Box
            key={i}
            sx={{
              position: 'absolute',
              top: `${Math.random() * 80 + 10}%`,
              left: `${Math.random() * 80 + 10}%`,
              animation: `float${i % 3 + 1} ${3 + i * 0.5}s ease-in-out infinite`,
              opacity: 0.6,
              '@keyframes float1': {
                '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
                '50%': { transform: 'translateY(-20px) rotate(180deg)' },
              },
              '@keyframes float2': {
                '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
                '50%': { transform: 'translateY(-30px) rotate(-180deg)' },
              },
              '@keyframes float3': {
                '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
                '50%': { transform: 'translateY(-25px) rotate(360deg)' },
              },
            }}
          >
            <Avatar
              sx={{
                bgcolor: ['#ff9800', '#4caf50', '#e91e63', '#2196f3'][i % 4],
                width: 40,
                height: 40,
                boxShadow: '0 4px 20px rgba(0,0,0,0.3)',
              }}
            >
              {[<Science />, <Engineering />, <Computer />, <Lightbulb />, <Rocket />, <Star />, <TrendingUp />, <AutoAwesome />][i]}
            </Avatar>
          </Box>
        ))}
      </Box>

      {/* Achievement Badges */}
      <Box
        sx={{
          position: 'absolute',
          top: 20,
          right: 20,
          zIndex: 4,
          display: { xs: 'none', md: 'block' },
        }}
      >
        {achievements.map((achievement, index) => (
          <Zoom
            key={achievement.title}
            in={animationStep >= 2}
            timeout={500 + index * 200}
          >
            <Card
              sx={{
                mb: 2,
                background: 'rgba(255,255,255,0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.2)',
                color: 'white',
                minWidth: 200,
                transition: 'transform 0.3s ease',
                '&:hover': {
                  transform: 'scale(1.05)',
                }
              }}
            >
              <CardContent sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Avatar sx={{ bgcolor: achievement.progress > 90 ? '#4caf50' : '#ff9800', mr: 1, width: 30, height: 30 }}>
                    {achievement.icon}
                  </Avatar>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.8rem' }}>
                      {achievement.title}
                    </Typography>
                    <Chip
                      label={achievement.level}
                      size="small"
                      sx={{
                        bgcolor: achievement.progress > 90 ? '#4caf50' : '#ff9800',
                        color: 'white',
                        fontSize: '0.7rem',
                        height: 20,
                      }}
                    />
                  </Box>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={achievementProgress > achievement.progress ? achievement.progress : achievementProgress}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    bgcolor: 'rgba(255,255,255,0.2)',
                    '& .MuiLinearProgress-bar': {
                      bgcolor: achievement.progress > 90 ? '#4caf50' : '#ff9800',
                    }
                  }}
                />
              </CardContent>
            </Card>
          </Zoom>
        ))}
      </Box>

      {/* Main Content */}
      <Container
        maxWidth="lg"
        sx={{
          position: 'relative',
          zIndex: 5,
          textAlign: 'center',
          color: 'white',
        }}
      >
        <Grid container spacing={4} alignItems="center">
          {/* Left Content */}
          <Grid item xs={12} md={6}>
            <Fade in={animationStep >= 1} timeout={1000}>
              <Box>
                <Chip
                  icon={<Star />}
                  label="🎯 Level Up Your Future"
                  sx={{
                    bgcolor: 'rgba(255, 152, 0, 0.2)',
                    color: '#ff9800',
                    fontWeight: 'bold',
                    mb: 3,
                    border: '1px solid rgba(255, 152, 0, 0.3)',
                    fontSize: '1rem',
                    py: 2,
                    px: 1,
                  }}
                />

                <Typography
                  variant="h1"
                  sx={{
                    fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
                    fontWeight: 900,
                    mb: 2,
                    background: 'linear-gradient(45deg, #fff 30%, #ff9800 90%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    textShadow: '0 0 30px rgba(255,255,255,0.5)',
                  }}
                >
                  Unlock Your
                  <br />
                  <Box component="span" sx={{ color: '#ff9800' }}>
                    Tech Powers
                  </Box>
                </Typography>
              </Box>
            </Fade>

            <Slide direction="up" in={animationStep >= 2} timeout={1000}>
              <Typography
                variant="h5"
                sx={{
                  fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.5rem' },
                  fontWeight: 400,
                  mb: 4,
                  opacity: 0.9,
                  lineHeight: 1.6,
                }}
              >
                🚀 Join the ultimate technical education adventure at NTSS!
                Master cutting-edge skills, unlock achievements, and level up your career in our gamified learning environment.
              </Typography>
            </Slide>

            <Zoom in={animationStep >= 3} timeout={1000}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap', mb: 4 }}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Rocket />}
                  onClick={() => scrollToSection('#about')}
                  sx={{
                    bgcolor: '#ff9800',
                    color: 'white',
                    px: 4,
                    py: 2,
                    fontSize: '1.1rem',
                    fontWeight: 'bold',
                    borderRadius: 3,
                    boxShadow: '0 8px 25px rgba(255, 152, 0, 0.4)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      bgcolor: '#f57c00',
                      transform: 'translateY(-3px)',
                      boxShadow: '0 12px 35px rgba(255, 152, 0, 0.6)',
                    }
                  }}
                >
                  🎮 Start Your Quest
                </Button>

                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<PlayArrow />}
                  sx={{
                    color: 'white',
                    borderColor: 'white',
                    px: 4,
                    py: 2,
                    fontSize: '1.1rem',
                    fontWeight: 'bold',
                    borderRadius: 3,
                    borderWidth: 2,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      bgcolor: 'white',
                      color: '#667eea',
                      transform: 'translateY(-3px)',
                      boxShadow: '0 8px 25px rgba(255, 255, 255, 0.3)',
                    }
                  }}
                >
                  🎬 Watch Demo
                </Button>
              </Box>
            </Zoom>
          </Grid>

          {/* Right Content - Stats Cards */}
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {stats.map((stat, index) => (
                <Slide
                  key={stat.label}
                  direction="left"
                  in={statsVisible}
                  timeout={500 + index * 200}
                >
                  <Card
                    sx={{
                      background: 'rgba(255,255,255,0.1)',
                      backdropFilter: 'blur(15px)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: 3,
                      p: 2,
                      transition: 'all 0.3s ease',
                      cursor: 'pointer',
                      '&:hover': {
                        transform: 'translateX(-10px) scale(1.02)',
                        background: 'rgba(255,255,255,0.15)',
                        boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar
                        sx={{
                          bgcolor: stat.color,
                          width: 50,
                          height: 50,
                          boxShadow: `0 4px 15px ${stat.color}40`,
                        }}
                      >
                        {stat.icon}
                      </Avatar>
                      <Box sx={{ flex: 1 }}>
                        <Typography
                          variant="h4"
                          sx={{
                            color: 'white',
                            fontWeight: 'bold',
                            fontSize: '2rem',
                          }}
                        >
                          {stat.value}
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{
                            color: 'rgba(255,255,255,0.8)',
                            fontWeight: 500,
                          }}
                        >
                          {stat.label}
                        </Typography>
                      </Box>
                      <IconButton
                        sx={{
                          color: stat.color,
                          animation: 'bounce 2s infinite',
                          '@keyframes bounce': {
                            '0%, 20%, 50%, 80%, 100%': { transform: 'translateY(0)' },
                            '40%': { transform: 'translateY(-10px)' },
                            '60%': { transform: 'translateY(-5px)' },
                          },
                        }}
                      >
                        <TrendingUp />
                      </IconButton>
                    </Box>
                  </Card>
                </Slide>
              ))}
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default HeroSection
