import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Button,
  useTheme,
  useMediaQuery,
  Grid,
  Card,
  CardContent,
  Fade,
  Slide,
  Paper,
} from '@mui/material'
import {
  School,
  EmojiEvents,
  Groups,
  Science,
  ArrowForward,
  Phone,
  Email,
} from '@mui/icons-material'

const HeroSection = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const [loaded, setLoaded] = useState(false)

  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  useEffect(() => {
    const timer = setTimeout(() => setLoaded(true), 300)
    return () => clearTimeout(timer)
  }, [])

  // Professional stats data
  const stats = [
    { icon: <School />, value: '15+', label: 'Years of Excellence' },
    { icon: <EmojiEvents />, value: '500+', label: 'Graduates' },
    { icon: <Groups />, value: '1200+', label: 'Current Students' },
    { icon: <Science />, value: '95%', label: 'Success Rate' },
  ]

  return (
    <Box
      id="home"
      sx={{
        minHeight: '100vh',
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        backgroundImage: 'url(https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          zIndex: 1,
        }
      }}
    >
      {/* Professional Main Content */}
      <Container
        maxWidth="lg"
        sx={{
          position: 'relative',
          zIndex: 2,
          py: 8,
        }}
      >
        <Grid container spacing={6} alignItems="center">
          {/* Left Content */}
          <Grid item xs={12} md={7}>
            <Fade in={loaded} timeout={1000}>
              <Box sx={{ color: 'white' }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontSize: '1.1rem',
                    fontWeight: 500,
                    mb: 2,
                    color: '#ff6b35',
                    letterSpacing: '0.1em',
                    textTransform: 'uppercase',
                  }}
                >
                  Welcome to Excellence
                </Typography>

                <Typography
                  variant="h1"
                  sx={{
                    fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem', lg: '5rem' },
                    fontWeight: 700,
                    mb: 3,
                    lineHeight: 1.1,
                    textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
                  }}
                >
                  Nyanza Technical
                  <br />
                  Secondary School
                </Typography>

                <Typography
                  variant="h5"
                  sx={{
                    fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.6rem' },
                    fontWeight: 400,
                    mb: 4,
                    opacity: 0.95,
                    lineHeight: 1.5,
                    maxWidth: '600px',
                  }}
                >
                  Empowering students with world-class technical education,
                  innovative learning environments, and comprehensive skill development
                  for tomorrow's technological challenges.
                </Typography>

                <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap', mb: 4 }}>
                  <Button
                    variant="contained"
                    size="large"
                    endIcon={<ArrowForward />}
                    onClick={() => scrollToSection('#about')}
                    sx={{
                      bgcolor: '#ff6b35',
                      color: 'white',
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: 2,
                      textTransform: 'none',
                      boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)',
                      '&:hover': {
                        bgcolor: '#e55a2b',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 6px 20px rgba(255, 107, 53, 0.4)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    Explore Programs
                  </Button>

                  <Button
                    variant="outlined"
                    size="large"
                    sx={{
                      color: 'white',
                      borderColor: 'white',
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: 2,
                      textTransform: 'none',
                      borderWidth: 2,
                      '&:hover': {
                        bgcolor: 'rgba(255,255,255,0.1)',
                        borderColor: '#ff6b35',
                        color: '#ff6b35',
                        transform: 'translateY(-2px)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    Contact Admissions
                  </Button>
                </Box>

                {/* Contact Info */}
                <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap', opacity: 0.9 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Phone sx={{ fontSize: 20 }} />
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      +250 788 123 456
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Email sx={{ fontSize: 20 }} />
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      <EMAIL>
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Fade>
          </Grid>

          {/* Right Content - Stats */}
          <Grid item xs={12} md={5}>
            <Slide direction="left" in={loaded} timeout={1200}>
              <Paper
                elevation={0}
                sx={{
                  background: 'rgba(255,255,255,0.95)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 3,
                  p: 4,
                  border: '1px solid rgba(255,255,255,0.2)',
                }}
              >
                <Typography
                  variant="h4"
                  sx={{
                    color: '#2c3e50',
                    fontWeight: 700,
                    mb: 3,
                    textAlign: 'center',
                  }}
                >
                  Our Achievements
                </Typography>

                <Grid container spacing={3}>
                  {stats.map((stat, index) => (
                    <Grid item xs={6} key={stat.label}>
                      <Box
                        sx={{
                          textAlign: 'center',
                          p: 2,
                          borderRadius: 2,
                          transition: 'transform 0.3s ease',
                          '&:hover': {
                            transform: 'translateY(-5px)',
                            bgcolor: 'rgba(255, 107, 53, 0.05)',
                          }
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            mb: 2,
                            color: '#ff6b35',
                          }}
                        >
                          {React.cloneElement(stat.icon, { sx: { fontSize: 40 } })}
                        </Box>
                        <Typography
                          variant="h3"
                          sx={{
                            color: '#2c3e50',
                            fontWeight: 700,
                            fontSize: '2.5rem',
                            mb: 1,
                          }}
                        >
                          {stat.value}
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{
                            color: '#666',
                            fontWeight: 500,
                            fontSize: '0.9rem',
                          }}
                        >
                          {stat.label}
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>

                {/* Additional Info */}
                <Box
                  sx={{
                    mt: 4,
                    pt: 3,
                    borderTop: '1px solid rgba(255, 107, 53, 0.1)',
                    textAlign: 'center',
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{
                      color: '#666',
                      fontStyle: 'italic',
                      mb: 2,
                    }}
                  >
                    "Excellence in Technical Education Since 2008"
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: '#999',
                      fontSize: '0.85rem',
                    }}
                  >
                    Accredited by Rwanda Education Board
                  </Typography>
                </Box>
              </Paper>
            </Slide>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default HeroSection
