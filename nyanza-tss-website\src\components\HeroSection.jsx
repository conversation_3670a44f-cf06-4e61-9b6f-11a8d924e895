import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Button,
  useTheme,
  useMediaQuery,
  Fade,
} from '@mui/material'
import {
  ArrowForward,
  Phone,
  Email,
} from '@mui/icons-material'

const HeroSection = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const [loaded, setLoaded] = useState(false)

  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  useEffect(() => {
    const timer = setTimeout(() => setLoaded(true), 300)
    return () => clearTimeout(timer)
  }, [])

  return (
    <Box
      id="home"
      sx={{
        minHeight: '70vh',
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        backgroundImage: 'url(https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          zIndex: 1,
        }
      }}
    >
      {/* Professional Main Content */}
      <Container
        maxWidth="lg"
        sx={{
          position: 'relative',
          zIndex: 2,
          py: 4,
          textAlign: 'center',
        }}
      >
        <Fade in={loaded} timeout={1000}>
          <Box sx={{ color: 'white', maxWidth: '800px', mx: 'auto' }}>
            <Typography
              variant="h6"
              sx={{
                fontSize: '1.1rem',
                fontWeight: 500,
                mb: 1.5,
                color: '#ff6b35',
                letterSpacing: '0.1em',
                textTransform: 'uppercase',
              }}
            >
              Welcome to Excellence
            </Typography>

            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4rem' },
                fontWeight: 700,
                mb: 2,
                lineHeight: 1.1,
                textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
              }}
            >
              Nyanza Technical
              <br />
              Secondary School
            </Typography>

            <Typography
              variant="h5"
              sx={{
                fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.4rem' },
                fontWeight: 400,
                mb: 3,
                opacity: 0.95,
                lineHeight: 1.4,
                maxWidth: '700px',
                mx: 'auto',
              }}
            >
              Empowering students with world-class technical education,
              innovative learning environments, and comprehensive skill development
              for tomorrow's technological challenges.
            </Typography>

            <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap', justifyContent: 'center', mb: 3 }}>
              <Button
                variant="contained"
                size="large"
                endIcon={<ArrowForward />}
                onClick={() => scrollToSection('#about')}
                sx={{
                  bgcolor: '#ff6b35',
                  color: 'white',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: 2,
                  textTransform: 'none',
                  boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)',
                  '&:hover': {
                    bgcolor: '#e55a2b',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 6px 20px rgba(255, 107, 53, 0.4)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Explore Programs
              </Button>

              <Button
                variant="outlined"
                size="large"
                sx={{
                  color: 'white',
                  borderColor: 'white',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: 2,
                  textTransform: 'none',
                  borderWidth: 2,
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.1)',
                    borderColor: '#ff6b35',
                    color: '#ff6b35',
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Contact Admissions
              </Button>
            </Box>

            {/* Contact Info */}
            <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap', justifyContent: 'center', opacity: 0.9 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Phone sx={{ fontSize: 20 }} />
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  +250 788 123 456
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Email sx={{ fontSize: 20 }} />
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  <EMAIL>
                </Typography>
              </Box>
            </Box>
          </Box>
        </Fade>
      </Container>
    </Box>
  )
}

export default HeroSection
