import React from 'react'
import {
  Box,
  Container,
  Typography,
  Grid,
  Link,
  IconButton,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
} from '@mui/material'
import {
  Facebook,
  Twitter,
  Instagram,
  YouTube,
} from '@mui/icons-material'

const FooterRiviera = () => {
  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: '#2c3e50',
        color: 'white',
        pt: 6,
        pb: 3,
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Report a Concern Section */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
              Report a concern
            </Typography>
            <Typography variant="body2" sx={{ mb: 3, color: 'grey.300' }}>
              The quickest way for us to respond to any concern is if you complete our concern raising form below.
            </Typography>
            
            <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                placeholder="Email"
                variant="outlined"
                size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'white',
                    '& fieldset': {
                      borderColor: '#ddd',
                    },
                  },
                }}
              />
              
              <FormControl size="small">
                <Select
                  defaultValue=""
                  displayEmpty
                  sx={{
                    backgroundColor: 'white',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#ddd',
                    },
                  }}
                >
                  <MenuItem value="" disabled>
                    Select Concern
                  </MenuItem>
                  <MenuItem value="Bullying">Bullying</MenuItem>
                  <MenuItem value="Harassment">Harassment</MenuItem>
                  <MenuItem value="Mental Health">Mental Health</MenuItem>
                  <MenuItem value="Neglect">Neglect</MenuItem>
                  <MenuItem value="Fraud">Fraud</MenuItem>
                  <MenuItem value="Other">Other</MenuItem>
                </Select>
              </FormControl>
              
              <TextField
                placeholder="Details"
                multiline
                rows={4}
                variant="outlined"
                size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'white',
                    '& fieldset': {
                      borderColor: '#ddd',
                    },
                  },
                }}
              />
              
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#1976d2',
                  color: 'white',
                  alignSelf: 'flex-start',
                  '&:hover': {
                    backgroundColor: '#1565c0',
                  },
                }}
              >
                Report Concern
              </Button>
            </Box>
          </Grid>

          {/* Logo and Site Map */}
          <Grid item xs={12} md={3}>
            <Box sx={{ mb: 3 }}>
              <img
                src="/ntss-logo.svg"
                alt="NTSS Logo"
                style={{ height: '60px' }}
              />
            </Box>
            
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              Site Map
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Link
                href="#principal"
                onClick={(e) => { e.preventDefault(); scrollToSection('#principal'); }}
                sx={{ color: 'grey.300', textDecoration: 'none', '&:hover': { color: 'white' } }}
              >
                Principal's message
              </Link>
              <Link
                href="#handbook"
                sx={{ color: 'grey.300', textDecoration: 'none', '&:hover': { color: 'white' } }}
              >
                Student Hand Book
              </Link>
              <Link
                href="#discover"
                onClick={(e) => { e.preventDefault(); scrollToSection('#discover'); }}
                sx={{ color: 'grey.300', textDecoration: 'none', '&:hover': { color: 'white' } }}
              >
                Discover NTSS
              </Link>
              <Link
                href="#admissions"
                onClick={(e) => { e.preventDefault(); scrollToSection('#admissions'); }}
                sx={{ color: 'grey.300', textDecoration: 'none', '&:hover': { color: 'white' } }}
              >
                Admissions
              </Link>
              <Link
                href="#fees"
                sx={{ color: 'grey.300', textDecoration: 'none', '&:hover': { color: 'white' } }}
              >
                Fees Structures
              </Link>
              <Link
                href="#student-life"
                sx={{ color: 'grey.300', textDecoration: 'none', '&:hover': { color: 'white' } }}
              >
                Student life
              </Link>
              <Link
                href="#alumni"
                onClick={(e) => { e.preventDefault(); scrollToSection('#alumni'); }}
                sx={{ color: 'grey.300', textDecoration: 'none', '&:hover': { color: 'white' } }}
              >
                Alumni
              </Link>
              <Link
                href="#privacy"
                sx={{ color: 'grey.300', textDecoration: 'none', '&:hover': { color: 'white' } }}
              >
                Privacy Policy
              </Link>
              <Link
                href="#faqs"
                sx={{ color: 'grey.300', textDecoration: 'none', '&:hover': { color: 'white' } }}
              >
                FAQs
              </Link>
            </Box>
          </Grid>

          {/* Follow Us and Contacts */}
          <Grid item xs={12} md={3}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              Follow us
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 4 }}>
              <IconButton
                sx={{ color: 'grey.300', '&:hover': { color: 'white' } }}
                aria-label="Facebook"
              >
                <Facebook />
              </IconButton>
              <IconButton
                sx={{ color: 'grey.300', '&:hover': { color: 'white' } }}
                aria-label="Instagram"
              >
                <Instagram />
              </IconButton>
              <IconButton
                sx={{ color: 'grey.300', '&:hover': { color: 'white' } }}
                aria-label="YouTube"
              >
                <YouTube />
              </IconButton>
              <IconButton
                sx={{ color: 'grey.300', '&:hover': { color: 'white' } }}
                aria-label="Twitter"
              >
                <Twitter />
              </IconButton>
            </Box>

            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              CONTACTS
            </Typography>
            <Typography variant="body2" sx={{ mb: 1, color: 'grey.300' }}>
              <strong>Email:</strong> <EMAIL>
            </Typography>
            <Typography variant="body2" sx={{ mb: 1, color: 'grey.300' }}>
              <strong>Tel:</strong> +250 788 123 456
            </Typography>
            <Typography variant="body2" sx={{ mb: 1, color: 'grey.300' }}>
              P.O. Box 123
            </Typography>
            <Typography variant="body2" sx={{ color: 'grey.300' }}>
              Nyanza, Rwanda
            </Typography>
          </Grid>
        </Grid>

        {/* Bottom Footer */}
        <Box
          sx={{
            mt: 4,
            pt: 3,
            borderTop: '1px solid #34495e',
            textAlign: 'center',
          }}
        >
          <Typography variant="body2" color="grey.400">
            Nyanza Technical Secondary School. All Rights Reserved
          </Typography>
        </Box>
      </Container>
    </Box>
  )
}

export default FooterRiviera
